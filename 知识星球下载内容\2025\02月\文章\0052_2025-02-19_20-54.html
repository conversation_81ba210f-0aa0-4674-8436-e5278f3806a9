
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0052_2025-02-19_20-54</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0052_2025-02-19_20-54</h1>
<div class="meta">Mt - 2025-02-19 20:54:23</div>
<div class="content">秀哥，想让你帮忙看看实习经历。<br/>这段实习经历按照星球里的模板写的，但是目前的成果描述是编的，因为实际上没跑过压力测试，项目组的测试同事也只是测试功能有没有bug，所以有点怕被问出破绽。</div>
<img src="../图片/0052_2025-02-19_20-54_0.jpg"/><div class="comment"><strong>【阿秀】回答：</strong><br/>这个写的很好，一看就很真实，没毛病，换我自己写估计也就这样了。<br/><br/>有一个问题那就是即使你自己不去测或者没条件测，自己编的数据也要做一下准备，比如你是用什么工具测的？有哪些步骤？这些问题也要提前准备好，编也要编的像一些，准备做齐一些。</div></body>
</html>
