
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0063_2025-01-21_11-07</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0063_2025-01-21_11-07</h1>
<div class="meta">哆啦A梦 - 2025-01-21 11:07:37</div>
<div class="content">秀哥好，我先说说我的情况，我是双非26届计算机硕士，但研究方向跟计算机没什么关系，cpp选手，打算开学之后找实习。目前是把黑马c++，侯捷的stl视频看了一遍，剑指offer刷了一遍，第二遍刷到一半因为各种事情已经有段时间没刷了，最近刚把牛客的webserver视频看完，我打算这几天把github上的那个tinywebserver和侯捷的书好好看看，过完年就开始投简历。可能是我看得太快太急，我总觉得这些项目好像明白又好像不明白，就怕面试时答不出来。我是23届本科毕业的嘛，那时候投简历基本都没回复，很惨，现在我也很怕找工作，怕自己不够强，不够优秀，到时候找不到工作。再加上我课题一直做不出来，虽然我们学校没有明确说要小论文，但是要是太混了也不一定敢说能毕业。总之，压力是很大的。<br/>我想问一下秀哥，我学完这些，是不是要去看一下中间件的知识，或者学一下go?有学习go和中间件的路线嘛？我连数据库都没怎么接触过，太难了，每天就早起晚归，也不知道干了个啥</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>webserver可以学但建议不要写在简历上作为项目了，有些烂大街了。。。25届已经很烂大街了，更不要说26届了。<br/><br/>实验室课题这个，你可以去打听一下你们师门往届有没有延毕的案例，如果有，那就需要当心了，如果没有一般是没什么事的，不要太摆一般是不会卡你毕业的。<br/><br/>因为你毕不了业，你们导师下年的招生名额就少一个，能拿去申请项目和国基的资本就少一个，所以除非是那种学生多的不要不要的研究生导师，一般是不会主动卡你毕业的。<br/><br/>你不学Go没必要去看中间件，你们现在能用得到的中间件一般就是限流、鉴权这几种常见的，有兴趣可以去了解下。<br/><br/>至于你说的数据库和Golang的学习路线，在星球都有的，置顶帖知识图谱中有Golang学习路线以及校招基础学科学习路线中可以找到这些内容。</div><hr/><h3>💬 评论 (3条)</h3><div class="comment"><strong>哆啦A梦</strong>: 我是老师的开山大弟子，啥都是从0做起，所以也很没底。我是看大家都说cpp找后端比较难找嘛，然后最好学个go，如果我学个go，这个简历该怎么写？go和cpp做的项目都放上去嘛？</div><div class="comment"><strong>阿秀</strong> 回复 <strong>哆啦A梦</strong>: 你如果主要cpp的，可以把golang在专业技能里提一下，然后做一个golang的项目在你的项目经验里；如果是找go的工作，那简历上的内容基本都是要写golang的了，在专业技能里提一下cpp就行</div><div class="comment"><strong>哆啦A梦</strong> 回复 <strong>阿秀</strong>: 好的，明白了，谢谢秀哥指点</div></body>
</html>
