
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0079_2025-01-25_16-38</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0079_2025-01-25_16-38</h1>
<div class="meta">切糕子 - 2025-01-25 16:38:42</div>
<div class="content">问问各位佬，在看TCPIP网络编程这本书的多播章节时，写的接收者receiver在运行时阻塞在recvfrom这条语句上了（如图，能输出横线，输出不了星号），输出不了文件信息。这是啥原因啊？[疑问]</div>
<img src="../图片/0079_2025-01-25_16-38_0.jpg"/><hr/><h3>💬 评论 (2条)</h3><div class="comment"><strong>切糕子</strong>: 多播广播出不了结果，救救孩子[苦涩]</div><div class="comment"><strong>切糕子</strong>: 解决了，把防火墙关了就好了。听我说谢谢你，因为有你（防火墙）</div></body>
</html>
