
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0035_2025-02-13_21-42</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0035_2025-02-13_21-42</h1>
<div class="meta">归去仍少年 - 2025-02-13 21:42:14</div>
<div class="content">秀哥你好，我是26届科班硕士，方向是C++，目前也是准备找今年的暑期实习了，对于下一步准备的侧重点我有些不太清楚，所以来咨询一下。<br/><br/>目前的进度是，简历基本已经写好，项目写了两个，一个是实验室项目，一个是星球里的通讯脚手架项目（源码部分读了一下，个人感觉对项目的理解还是比较浅）；力扣刷题和代码随想录这些平时也一直都在做，力扣大概刷了200多，代码随想录刷了一半；然后就是八股方面，由于我之前本科的时候学过计网操作系统这些，然后就是简单过了一遍计网、操作系统、数据结构、设计模式（看了理解了一遍），目前在看星球里的MySQL必知必会。<br/><br/>我主要想问一下：<br/>1、通讯脚手架项目是否还需要再深入看看源码理解一下呢？还是就背一下可能被问到的问题<br/>2、数据库的话有必要把MySQL必知必会全部过一遍吗？还有Redis没咋学，该怎么办<br/>3、其他像计网数据库这些八股，是直接着手背八股题目么？八股背题应该背哪些内容呢（好像在星球资源里没有看到）<br/><br/>感谢秀哥解惑</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>师弟好<br/>1、如果你有时间自己过一下是最好的，自己走一遍过来，这样是最好的，理解会最清楚；如果没这个时间和精力，也建议跟着数据流走一遍过来，搞懂这个项目是怎么运行的。至于文档中记录的一些高频问题，肯定要背熟的。<br/><br/>2、mysql必知必会是讲SQL的，教你如何写SQL的，不是教你如何学数据库的，索引、主键、优化这些知识点你在这本书上看不到的。<br/>数据库这块的学习可以看下星球置顶帖知识图谱中的校招基础学科学习路线，其中有数据库这块怎么学的，可以看看。<br/><br/>3、八股文这些内容在星球的置顶帖资源沉淀中有，可以去看看。直接背在面试中很容易被戳穿，建议从理解的角度去看待八股文，而不是去死记硬背。</div></body>
</html>
