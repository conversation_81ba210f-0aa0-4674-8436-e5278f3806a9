
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0027_2025-01-08_22-10</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0027_2025-01-08_22-10</h1>
<div class="meta">只想摸鱼 - 2025-01-08 22:10:41</div>
<div class="content">秀哥，进来潜水也一个月了，目前自学的有些迷茫，想请教下学习进度和方向问题 本人是非科班转码（虽然是非科班，但学过的专业必修课也有JAVA程序设计，数据结构，数据库原理），目前研一，本211，硕985，有一段三个月Python爬虫开发的实习经历，想从事C++后端，暂定的学习计划：<br/>***研一上+寒假***（《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=C++Primer Plus" style="color: #007acc;">C++Primer Plus</a>》接近尾声，预计能够完成）：<br/>操作系统：书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=计算机是怎样跑起来的" style="color: #007acc;">计算机是怎样跑起来的</a>》<br/>计算机网络：书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=网络是怎样连接的" style="color: #007acc;">网络是怎样连接的</a>》<br/>数据库：书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=SQL必知必会" style="color: #007acc;">SQL必知必会</a>》和《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=MySQL是怎样运行的" style="color: #007acc;">MySQL是怎样运行的</a>》<br/>C++：《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=C++Primer Plus" style="color: #007acc;">C++Primer Plus</a>》+视频“侯捷老师的C++课程”（STL泛型编程、C++11新特性、内存管理与分析）<br/>***研一下***：<br/>操作系统：视频“南京大学计算机系统基础(一)(二)”<br/>计算机网络：书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=计算机网络·自顶向下方法" style="color: #007acc;">计算机网络·自顶向下方法</a>》前六章<br/>数据库：视频“Redis尚硅谷B站教程”<br/>C++：书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=C++Primer" style="color: #007acc;">C++Primer</a>》、《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=STL源码剖析" style="color: #007acc;">STL源码剖析</a>》、《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=Effective C++" style="color: #007acc;">Effective C++</a>》、《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=深度探索C++对象模型" style="color: #007acc;">深度探索C++对象模型</a>》第三章<br/>数据结构：书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=大话数据结构" style="color: #007acc;">大话数据结构</a>》+刷leecodeHot100<br/>***研一暑假***：<br/>老师给一次实习机会，想争取下能不能两次，如果能这个暑假就找中小公司实习，如果不能就投投简历找面试攒下经验<br/>***研二上***：<br/>操作系统：书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=深入了解计算机系统" style="color: #007acc;">深入了解计算机系统</a>》<br/>计算机网络：复习书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=计算机网络·自顶向下方法" style="color: #007acc;">计算机网络·自顶向下方法</a>》<br/>数据库：书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=Redis设计与实现" style="color: #007acc;">Redis设计与实现</a>》（选看）<br/>C++：书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=TCP/IP 网络编程" style="color: #007acc;">TCP/IP 网络编程</a>》和《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=Linux高性能服务端编程" style="color: #007acc;">Linux高性能服务端编程</a>》，寻找一个项目完成<br/>数据结构：秀哥网站“精选力扣300+道算法题”<br/>***研二寒假***：<br/>可以复习也可以去投下简历攒面试经验<br/>***研二下***：<br/>操作系统：复习书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=深入了解计算机系统" style="color: #007acc;">深入了解计算机系统</a>》<br/>计算机网络：复习书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=计算机网络·自顶向下方法" style="color: #007acc;">计算机网络·自顶向下方法</a>》<br/>数据库：复习书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=SQL必知必会" style="color: #007acc;">SQL必知必会</a>》和《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=MySQL是怎样运行的" style="color: #007acc;">MySQL是怎样运行的</a>》、视频“Redis尚硅谷B站教程”<br/>C++：复习书籍《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=C++Primer" style="color: #007acc;">C++Primer</a>》和《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=STL源码剖析" style="color: #007acc;">STL源码剖析</a>》，选看《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=C++沉思录 " style="color: #007acc;">C++沉思录 </a>》、《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword= C++ 模版元编程 " style="color: #007acc;"> C++ 模版元编程 </a>》以及《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=C++并发编程实战" style="color: #007acc;">C++并发编程实战</a>》<br/>这一阶段还要完善简历+投简历<br/>***研二暑假***<br/>实习<br/>以下是我的问题，麻烦秀哥帮忙查看下我的学习计划有哪些可以改进的地方？目标是进腾讯后端开发，不知道机会大吗？</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>👍🏻你这进度相当ok，一看就是有认真看学习路线文章，我个人觉得你唯一需要做的就是按部就班走下来。<br/><br/>这一套走下来的话，结合你的背景，进腾讯基本是稳的。</div><hr/><h3>💬 评论 (4条)</h3><div class="comment"><strong>只想摸鱼</strong>: 谢谢秀哥！我现在学习就更有动力了！</div><div class="comment"><strong>阿秀</strong> 回复 <strong>只想摸鱼</strong>: 没事，不过考虑到你现在刚研一，后面也是以腾讯为目标，可以试试闲暇时间学一下golang，双技术栈。<br/><br/>据我所知现在很多鹅厂的新项目，尤其是业务项的业务首选都是golang了。</div><div class="comment"><strong>Zeroxus</strong> 回复 <strong>阿秀</strong>: 很对，之前面的cpp岗，面试官说进来转go[捂脸]</div><div class="comment"><strong>只想摸鱼</strong> 回复 <strong>阿秀</strong>: 好！谢谢秀哥的补充，我看看之前什么时候学有余力了就安排go的</div></body>
</html>
