
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0074_2025-02-26_20-00</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0074_2025-02-26_20-00</h1>
<div class="meta">白也 - 2025-02-26 20:00:08</div>
<div class="content">秀哥<br/>我先来介绍一下我的基本情况，我现在在一个公司担任开发工程师，平时就是Linux下进行一些脚本编写，以及软件调试<br/>然后我想报名软考，我底子也不太行，所以说，我想问一下秀哥有没有什么学习的方法或者资源，麻烦秀哥了[抱拳]<span style="color: #ff6b6b; font-weight: bold;">##软考##</span> <span style="color: #ff6b6b; font-weight: bold;">##C++##</span></div>
<hr/><h3>💬 评论 (2条)</h3><div class="comment"><strong>阿秀</strong>: 下回再有问题可以点左上角的去提问按钮，这样系统会给我发通知。<br/><br/>我去年下半年考了两个证，一个是上海的计算机程序设计员，一个是软考高级；前者是公司帮我报名的，通过后有补贴，后者是我跟我媳妇儿一块考的，我考的是高级，架构设计师那个。<br/><br/>因为我一直在一线写码，没跟很多朋友一样转方向去做管理了，所以我的代码能力还行，我也没报什么班，就自己考的。我就找极客时间的运营要了他们的软考pdf资料和近5年软考真题，你有需要的话也可以找他们领一下，扫下图的二维码就可以领。<br/><br/>然后我就把这些pdf打印出来了，这样方便自己看。有时候上下班坐地铁的时候也会看看，我个人觉得还行，不算很难，然后就过了，主要是SQL有些手生，其余的倒都还好。如果你是开发的话，这些内容对你来说应该比较简单，不算难。<br/><br/>然后是考中级还是高级的问题了，我考的是高级，性价比高一些，一步到位的，我的经验大概就是这些了。</div><div class="comment"><strong>白也</strong> 回复 <strong>阿秀</strong>: 好的，谢谢秀哥，第一次提问，不太清楚[抱拳]感谢秀哥</div></body>
</html>
