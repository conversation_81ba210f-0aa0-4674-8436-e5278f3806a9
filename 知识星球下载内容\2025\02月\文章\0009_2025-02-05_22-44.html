
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0009_2025-02-05_22-44</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0009_2025-02-05_22-44</h1>
<div class="meta">Six feet u* - 2025-02-05 22:44:30</div>
<div class="content">秀哥你好，本人目前入职刚刚好一年半，民办本，游戏后端方向，目前薪资较低是8k;<br/><br/>进了游戏行业发现加班压力大，并且项目很容易黄，很不稳定，<br/>还有在游戏开发中，后端不是主要部分，但是线上容易出事故，看了身边的服务器主程情况，觉得并不是一个好的职业方向；<br/><br/>之后通过脉脉、牛客等渠道也询问了一些更老资历的服务端程序，他们给我的建议是如果转不出游戏就转游戏客户端开发，<br/>但我自己觉得游戏客户端开发也是一样，加班多，不稳定，并不是一个好的职业方向，并且要转付出的努力也不少，unity也得重新学；<br/><br/>虽然觉得这样不行，但还是浑浑噩噩地有一天算一天过，直到过完年才幡然醒悟，这样下去可能要废了；<br/><br/>所以想问问秀哥，如果要转别的程序方向，有什么方向适合社招转？<br/>或者想要考研重开，有什么程序类的轻松一些的工作方向能推荐？<br/>或者秀哥有别的看法能指点我下😂，真是有点茫然<br/><br/>目前对于跳槽的准备，八股基本忘记了，leetcode倒是一直有坚持刷，按照秀哥的网站算法指南已经有400多道题的积累了<br/>项目基本是空的，还是校招时期的webserver，不清楚哪些项目能写上，也很头疼</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>游戏行业就是会有这种问题，加班多+行业窄， 下家公司基本还是做游戏这些行业。<br/><br/>你下家如果不想再做游戏行业了，可以试试从游戏服务端转向服务端开发，可能会好转一些，跟语言没什么关系，如果还想做技术的话，服务端开发是优解。如果你不想做技术了，可以转测试，这个应该是难度最小的。<br/><br/>做互联网没有轻松的方向。。。。做非技术的容易被取代，没有安全感；做技术难逃加班，所以这行就这样，薪资给你了也要你付出的，资本家远比我们要精明得多。<br/><br/>可以看下星球置顶帖资源沉淀中的几个速成项目，脚手架项目好几个社招1-2年的都拿去用，并且用上了的。</div></body>
</html>
