
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>0005_2025-01-02_21-26</title>
            <style>
                body { font-family: 'Microsoft YaHei', <PERSON>l, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
        </head>
        <body>
            <h1>0005_2025-01-02_21-26</h1>
            <div class="meta">宁海佳 - 2025-01-02 21:26:02</div>
            <div class="content"><span style="color: #ff6b6b; font-weight: bold;">##C++##</span> <span style="color: #ff6b6b; font-weight: bold;">##考研/保研##</span> <span style="color: #ff6b6b; font-weight: bold;">##每日一点点##</span> <br>c++day3<br></e><br><br>值传递<br><br>b=a  因为是值传递，程序员保证两边的类型是一致的<br><br>int[4] *  数组指针   指向长度为4的一维整型数组的指针<br>int *    整型指针    指向一个整型元素的<br>指针的定义<br>取地址 ，取值<br>使用场景  传递  偏移<br>指针与自增自减<br>指针与一维数组</div>
        </body>
        </html>
        