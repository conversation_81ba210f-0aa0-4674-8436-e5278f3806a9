
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0080_2025-02-27_12-01</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0080_2025-02-27_12-01</h1>
<div class="meta">橙汁 - 2025-02-27 12:01:15</div>
<div class="content">秀哥，秋招提前来实习一段时间了，最近几天加班真的加麻了，我们这边提前实习是有考核的，跟试用期差不多，然后如果主管觉得考核不行三方就解约了<br/>现在这几天白天是干不完活，晚上还要干活加准备业务上的考核，这几天下班后回宿舍也加班到十一二点<br/>虽然确实比刚来的时候进步很多，但是项目实在是太大了，还是有非常多会有问题，自己加班容易一两个小时都找不到问题，很有挫败感，之前的实习压力比起来不是一个量级的，感觉这样下去会先扛不住压力，有没有什么好办法</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>”虽然确实比刚来的时候进步很多“：看来确实是有收获，要不不会这么说hhh。<br/><br/>现在去实习接触这些东西，提前有个缓冲buffer，远比校招毕业后直接入职去接触业务要来的好，如果是后者的话，压力可能会更大一些。<br/><br/><br/>我就是校招毕业后直接去的字节，结果刚开始那一两个月基本每天晚上都要学到一两点去上手技术。。。太痛苦了！不过就那几个月，挺过去就好多了。我说这些不是在pua你，让你习惯痛苦之类的，确实是我也是这样过来的。。。<br/><br/>”自己加班容易一两个小时都找不到问题“：这种问题优先自己定位，你只有在定位过程中才能有进步，如果实在找不到，那就寻求帮助，实习生找老员工或者组长求助不用觉得丢脸或者不好意思，优先找mentor寻求帮助，带你是他的责任。</div></body>
</html>
