
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0062_2025-01-21_10-29</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0062_2025-01-21_10-29</h1>
<div class="meta">ctrl - 2025-01-21 10:29:46</div>
<div class="content">秀哥好，为了准备秋招，从前几天开始在刷题。想请教一下秀哥正确的刷题方法是什么呢？需要leetcode题解的每一个方法都学会吗？谢谢秀哥。</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>不用，最重要的是要会做，对于大多数人来说，刷题不是为了追求极致的空间和时间性能，刷题的目的是为了面试和找工作。<br/><br/>每道leetcode下的解题方法有很多，能做出来的话就用自己的方法做，做不出来就找一种你能理解的方法，看看它是怎么做的，然后跟着做就行。<br/><br/><br/>我个人的刷题路线是按照专题刷，每个专题从easy开始，10题左右就换medium级别的，一个专题大概刷30-50道题左右。然后换下一个专题，每周末会复习这周刷过的错题，然后周而复始。<br/><br/>我比较看重复习，很多经典的题我甚至三刷四刷过。</div><hr/><h3>💬 评论 (1条)</h3><div class="comment"><strong>ctrl</strong>: 好的，谢谢秀哥[愉快]</div></body>
</html>
