
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0035_2025-01-13_11-35</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0035_2025-01-13_11-35</h1>
<div class="meta">奔行世间 - 2025-01-13 11:35:14</div>
<div class="content">秀哥好，我是一名普通一本计算机专业的大四学生，24年年底考完研，但觉得自己上岸概率不大。想直接准备春招，找工作了；但考研结束后，一直没找到合适的实习。学过c/c++网络编程，会写python、golang的一点皮毛。希望去大厂做个开发的螺丝钉。接着学c/c++网络编程怕自己一两个月到不了可以找工作的水平，也不知道c/c++网络编程可以投什么岗位，python、golang会的也真的不多，没有在谦虚。求秀哥指路！</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>学弟，看得出来你过去一年都有在认真考研，准备考研的事情，没怎么关注过计算机专业的秋招情况。。<br/><br/>事实上，即使你技术不错，而且有实习经历想去做大厂做开发也不是一件容易的事了，可以去我公众号回复 “性价比”， 有一篇文章应该很适合现在的你，讲的是cpp技术岗要找工作所需要的最低技能了。<br/><br/>你现在能做的性价比最高的事就是抓紧我的学习笔记网站好好利用。</div></body>
</html>
