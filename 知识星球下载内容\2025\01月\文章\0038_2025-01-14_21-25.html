
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>0038_2025-01-14_21-25</title>
            <style>
                body { font-family: 'Microsoft YaHei', <PERSON>l, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
        </head>
        <body>
            <h1>0038_2025-01-14_21-25</h1>
            <div class="meta">Alan - 2025-01-14 21:25:48</div>
            <div class="content"><span style="color: #ff6b6b; font-weight: bold;">##每日一点点##</span><br>day18<br> 1.看了脚手架的项目介绍<br>2.json_cpp<br>A.头文件、json.cpp  jsonValue写完了<br>B.paser 中数字、16进制<br>3.刷了两题栈</div>
        </body>
        </html>
        