
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0017_2025-01-06_12-59</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0017_2025-01-06_12-59</h1>
<div class="meta">DUZHANGWEN - 2025-01-06 12:59:50</div>
<div class="content">秀哥，想请教个问题，我是双非本科，成绩不是很好，代码能力一般，秋招拿到了三个offer，一个是腾讯子公司腾娱的测开岗位，主要是做c++压测关于后台性能相关的，目前在实习中，团队氛围特别好，不管是导师还是领导同事都非常给力，也不卷，在武汉每天6点下班.还有的offer是做网络安全的，最大的困惑就是工资这方面可能觉得还是少了一点，税后可能就只有10K了，我在想努努力春招有没有可能找到待遇稍好一点的工作，城市可能对我没那么重要，期待薪资在15K左右.现在既有学校毕业设计、毕业论文、又要实习，可能精力确实没有那么多，想着还是往C++后台和测开这方面去找，也不知道游戏测开和互联网的测开差别在哪，然后重点精力应该去准备哪些🥹</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>当然可以继续找下去，这个没问题的，对自己的薪资不满意那就春招好好加油继续找就行。<br/><br/>我个人是建议把你现在的这段实习经历可以考虑写在简历上，增加简历含金量。<br/><br/>我理解你现在是拿了offer，提前去实习了，提前去感受一下部门和工作氛围的。游戏测开算测开的一种，要说区别，只能是业务不一样带来的区别。如果你现在实习所在的部门不错，一定程度上是能抵消薪资的。<br/><br/>建议重点放在实习经历，也就是可以放在你简历上的内容，以及算法和八股不要落下了，坚持刷就行，其余就没什么了。<br/><br/>还有就是可以复盘下秋招期间的面试，找找面试没过的症结所在，某种意义的查漏补缺吧。</div></body>
</html>
