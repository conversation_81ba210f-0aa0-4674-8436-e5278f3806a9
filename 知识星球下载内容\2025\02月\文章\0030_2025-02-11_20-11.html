
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0030_2025-02-11_20-11</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0030_2025-02-11_20-11</h1>
<div class="meta">归寻 - 2025-02-11 20:11:02</div>
<div class="content">秀哥好，因为周四晚上又要面字节飞书客户端了，有几个的问题还是想问问。<br/><br/>秋招面了五轮，得有2-3轮面试官一开始问到了对客户端的看法，我当时没详细准备这方面说辞，说的有点心虚。。。下面六个这类问题应该怎么说比较好。<br/><br/>我第一个项目跟群2八股侠24届秋招一样的付费项目(我跟他要过项目面经，他说他没详细记这个项目面经，说忘的差不多了。。。)，只不过名字不一样他的是数据中心、我的名字就是气象平台。面试官应该见的不多，秋招的时候不少面试官还是蛮喜欢聊这个项目的。有几个偏架构的问题还是希望秀哥解答一下。<br/><br/># 客户端的看法<br/>1. 对客户端的一些感知是什么样子的，了不了解？<br/><br/>2. 就是你本身对那个对客户端的一个感知，比如说安卓还是iOS，你自己的一个情况，比如说安卓了不了解iOS，了解以及未来你更看好哪一个方向？类似于这种。<br/><br/>3. 你后端技术栈为什么要投客户端呢<br/><br/>4. 你其实本身这个是一个客户端的岗位，然后其实也有客户端后端以及前端，然后你对他们有一些基本的认识和了解吗<br/>5. 就是客户端的了解，是说它其实分成 iOS 和安卓两种，嗯，然后它和前端之间的差异你有了解吗？<br/><br/>6. 然后你写过安装或者 iOS 吗<br/><br/># 项目架构的一些问题<br/>1. 项目怎么交付啊？<br/>2. 质量怎么保证啊？(当时深信服测试岗二面面试官侧着身子，一脸不屑语气轻蔑的问我这两个问题。。)<br/>3. 整体架构是你设计的，还是你导师那边设计的？(这个我说是学校导师接的项目应该没啥问题吧，他还是问整体架构是谁设计的)<br/>4.因为不同的服务模块，他的负载什么啊他可能都不一样，对不对？那你怎么考虑的这个负载或者怎么做负载的（字节二面问的，这个答得不好，二面当时也过了。当时面试蛮多的，忘了问秀哥，面完我就问了一下gpt，他说的是切入点可以说一说自己每个项目模块是IO密集型还是计算密集型，我现在看感觉gpt说的有点虚。。。这个还是问秀哥看看怎么回答比较好呢）</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/># 客户端的看法<br/><br/>1、一些你对于客三消的看法，比如“网上有人都说客三消，但我不认为客三消，20年之前就有这种说法，这都25年了，这个行业依然存在，不认为这个行业的未来是单薄的”。然后说一些客户端的职责之类的，这些可以去问百度或者gpt。<br/><br/>2、你是写cpp的，那就投递ios开发；你是学java的就投安卓。我是ios，愿意去了解安卓，反之亦然。更好看安卓，因为生态更好。<br/><br/>3、直接点，直接说后端竞争太大就行，大多数时候简单直接比虚情假意要好的多。<br/><br/>4、这个问题你应该结合你自身的情况来回答吧。。。这个没什么固定的说法。<br/><br/>5、同4<br/><br/>6、一样同4，如果打算投递ios，可以试着安装一下，拿半天时间试着上个手，把环境搭一下、<br/><br/># 项目架构的一些问题<br/><br/>1、直接说这是老师接的校外项目，我们去对方研究所/公司安装的，并且现场演示。<br/><br/>2、这个质量是什么意思？项目质量还是代码质量？如果是前者就说试运行半年或者三个月，目前没出过事；如果是后者就说一些项目组约定的一些代码规范、<br/><br/>3、了解就说你有参与，然后找一个你觉得不错的模块，说这块是你主导的；如果不了解，就把这部分上一届或者上上一届的师兄师姐。<br/><br/>4、我建议别给自己挖坑，就说使用人数不多，设计的时候甲方就说了使用人数一共就不到100人，所以可能不会有什么很高的IO。<br/><br/>相信我，大多数面试官都不喜欢不懂装懂，还要硬扯的人，说不清楚或者不懂就把这部分推给前人。</div></body>
</html>
