
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0014_2025-01-04_23-37</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0014_2025-01-04_23-37</h1>
<div class="meta">五十吾 - 2025-01-04 23:37:13</div>
<div class="content">阿秀学长，我找到了提问的地方啦！主要是想向您了解一些行业择业经验和建议！<br/><br/>本人是非科班小白转码，目前研二，本211，硕985，因为前期对就业环境理解不到位，危机感不足，感觉自己啥也不会(就是那种练计算机基础都没系统学过的程度)，马上就要面临实习秋招了，之前简单调研了一下师兄师姐转行经验，正在学习java，目前想问一些有关岗位定位和学习注意事项。<br/><br/>1.本人的这个情况目前开始准备，上岸可能性比较大的技术岗有哪些哇？<br/><br/>2.因为对自我能力和准备时间比较悲观，再加上本人对互联网大厂执念并不深，只是期望能有合适的岗位就行(薪资25w+，有一定的晋升空间)，所以想具体问一下这个需求有哪些可以选择的岗位方向，或者换句话说，朝着哪个方向去找工作会比较合适？(之前定位的是国企的技术开发岗，后端开发)<br/><br/>3.目前学习是否有需要注意的地方，比如是优先加强java技术，自己多练习一些项目开发，还是也需要花费一部分时间补足计算机基础学科的学习呢？同时找实习的时间是否要等所有上述内容都差不多了再开始呢？还是也可以一边学习一边大胆投实习？<br/><br/>4.我个人的情况其实有点糟糕，因为科研几乎没怎么推进(我导师临时换题目)，不过没有小论文发表的苦恼，我导师想让我把科研推进差不多再考虑实习。这也注定了我没办法全身心准备第3点的学习，在这种情况下，我的转行道路是否会被严重影响？<br/><br/>ps：关注了阿秀虽然没多久，在互联网冲浪看到阿秀的帖子，真的感觉到非常有共鸣感，非常期望阿秀学长的回复！</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>师弟你好<br/><br/>1、你现在准备的不算很早，但相较于很多四五月份才开始准备找工作事宜的人来说已经好很多了。<br/><br/>一般来说，你能找到的工作，跟你前一届师兄师姐找到的比较像，所以你可以细细打听一下你的师兄师姐们的出处，如果觉得不满意，就最好多上心一些。<br/><br/>上岸可能性比较大的技术岗最多的应该就是通用开发，也就是大多数人口中的后端研发，而且你的背景不错，可以先按照后端研发的路子走。<br/><br/>2、期望能有合适的岗位就行(薪资25w+，有一定的晋升空间)：这个期望，如果是国企比较难，国企能给你开到20就算很不错的了，25w的国企岗位一般都是领导级别了；如果是互联网的话，这个期望不算难，后端、前端、测开都可以做到，如果是想要有晋升空间，优先后端一些。<br/><br/>3、java技术和计算机基础应该是同步学习的，你现在不学基础，就等着后期直接背八股文的话，很容易就会在面试中被戳穿，就我所辅导过的历届学生而言，能拿到多个offer的基本基础都很不错，都是按部就班自己学过来的。<br/><br/>现在开始按照星球置顶帖知识图谱中的校招基础学科学习路线和Java学习路线走的话，四五月份就可以考虑去找实习了，当然这一切的前提是你有认真学，而不是三天打鱼两天晒网。<br/><br/>4、会，不要跟你的导师说你找工作的计划、诉求、转行等等，因为帮助不大，大多数转行都是自己偷偷学自己的，我学的时候都是晚上学到十一二点，白天时间都用来科研了，晚上八点之后开始学自己的，周六周日+节假日很少出去玩，都利用起来了。</div><hr/><h3>💬 评论 (2条)</h3><div class="comment"><strong>五十吾</strong>: 谢谢阿秀的回答！<br/>但是我还是有一些小疑问，那是不是必须把握住校招“金九银十”的时间？万一没有找到合适的实习，在秋招中岂不是优势非常小了[发呆]再加上现在实习要求普遍离谱，都要求有一段实习背书，按照这个准备路线，在校招前感觉感觉能有一段实习经历就很不错了？</div><div class="comment"><strong>阿秀</strong> 回复 <strong>五十吾</strong>: 对的，四五月份可以考虑去找暑期实习的</div></body>
</html>
