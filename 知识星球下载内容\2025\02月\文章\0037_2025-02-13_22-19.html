
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0037_2025-02-13_22-19</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0037_2025-02-13_22-19</h1>
<div class="meta">欧阳天培 - 2025-02-13 22:19:56</div>
<div class="content">秀哥元宵节快乐。去年刚实习的时候请教您我要不要开始边准备26届的秋招，现在想想您当时说的很对，进去以后发现真的是拼尽全力才稳住手上的工作，产出论文。接下来估计还得继续实习到6月，期间还得再产出一篇论文（这个主要是为了给导师有所产出，校企合作关系，之前那篇是公司的）。<br/><br/>现在感觉不得不开始准备9月秋招了，但是还没有下定决心走哪个方向，因为实习的是推荐算法岗，我本人也没有什么后端的项目经历和基础，其实有点想尝试继续做算法，但也明白这方面门槛相对更高，想听听秀哥的意见。<br/><br/>目前的基础：半年实习下来对tf2，sql，spark比较熟悉了，但是没有接触线上的事情（实习生无权限），然后参与的项目方向是llm with rec。</div>
<img src="../图片/0037_2025-02-13_22-19_0.jpg"/><div class="comment"><strong>【阿秀】回答：</strong><br/>去年你问我这个问题的时候，我就不太看好你当时的想法。。。算法岗实习和开发岗不一样的。<br/><br/>看你这个说法要实习到九月，我的建议是试着走实习转正这条路，如果没有转正的希望就早做打算，不要等到八月份才知道自己没有希望转正，你可以就当做自己没有机会实习去准备着，永远不把鸡蛋放在一个篮子里，要尽早给自己留后路。</div></body>
</html>
