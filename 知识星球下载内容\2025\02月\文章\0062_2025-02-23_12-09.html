
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0062_2025-02-23_12-09</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0062_2025-02-23_12-09</h1>
<div class="meta">浅唱SnAn - 2025-02-23 12:09:17</div>
<div class="content">秀哥你好！我是26届的毕业生，今年研二，年前也向秀哥咨询过几次问题，现在是在一家初创企业做数据库开发的实习，然后目前有一些疑惑和不确定的想请秀哥指点一下。<br/>1.目前在这家公司实习了快三个月，计划是三月底跑路然后准备暑期实习的招聘，但是我目前在这家公司做的工作好像也就是给他们自研的数据库写了两个小功能，感觉写在简历上的东西好像没多少，不知道在后续的暑期实习招聘中会不会存在比如面试官觉得我实习四个月就做了这么点东西然后反而会留下比较差的印象的情况，也想问问我这种感觉没做什么东西的情况正常吗。<br/>2.因为之前一直是走c++的，但是在公司用的是golang，所以我后面可能打算c++和golang双修一起投递岗位，想问一下秀哥在后面面试的过程中如果涉及到手撕算法题可以只用c++来做吗，因为算法题一直是用c++来练的，做算法题的熟练度也是c++高，有点不是很想用golang做算法题。<br/>3.我可能打算在四月中旬这个时间开始投递暑期实习的简历，想问一下秀哥这个时间晚不晚，因为我可能还需要一点时间去复习一下之前遗忘的八股，算法题也需要复习。<br/>麻烦秀哥帮忙解惑，非常感谢秀哥！</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>师弟你好<br/><br/>1、很多人实习三个月就走人了，你超过3个月其实已经比很多人好了；实习大多数工作都是打杂，甚至很多都没写代码的机会，自然感觉没什么东西可以写在简历上的感觉了。<br/><br/>很多人就会考虑找一个功能包装一下说是自己做的，搞清楚前因后果，你也可以试着这样干。况且说，你还做了两个小功能了，已经比很多人好很多了。<br/><br/>2、可以这样做，但建议你也要试着用golang解题，因为有的面试官不介意你用什么语言去刷，但有的会要求你必须要用相匹配的语言去做题才行。你可以不用，但当有需要的时候，你也要用能用golang做出来题的能力。<br/><br/>3、字节已经开25届的春招，个人估计再有大半个月也就是到三月中下旬的时候会开26届的实习，四月多投递是完全来得及的。<br/><br/>只有大厂会开的早一些，暑期实习一般在三四月份就会开始，一些中小公司都是五月之后才会开始招暑期实习的。</div></body>
</html>
