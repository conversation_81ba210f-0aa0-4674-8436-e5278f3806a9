
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0045_2025-02-17_09-59</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0045_2025-02-17_09-59</h1>
<div class="meta">Kingzz - 2025-02-17 09:59:25</div>
<div class="content">提问：秀哥，我是深大24届毕业的硕，之前一直在实验室做科研助理打算申博，但是12月终究被老师放鸽子了，12月下旬开始准备C++后端开发（头铁），目前leetcode 陆陆续续刷了200道，hop100在过第二遍。侯捷老师的课基本上都看了，大部分也能消化，MySQL也看完了，跟着韩磊做了2个项目：集群聊天和RPC。也手写了三大池，Muduo网络库等等，目前就差 Redis 和一些中间件，以及一些细小问题的八股。最近一直在 boss 应聘，但是连投出去的简历都比较少，内心有点扛不住了。想问问几个问题：<br/>1. 我后续的复习计划是花10天左右整理学过的知识点，针对性加强面试过程中的回答框架。然后再补上 Redis ，将RPC和聊天服务器融合成一个项目，并补多一个大项目，大约到3月中旬可以完成。<br/>2. MySQL 我只是对八股内容比较熟悉，需要做一下CMU的数据库实验吗，因为我应该只能走社招了，后续的准备是以八股为主，还是以实践能力为主呢？<br/>3. 之后的应聘计划应该如何做？ 目前实在是头皮发麻了，能走校招的企业寥寥无几，社招又要工作经历。</div>
<hr/><h3>💬 评论 (1条)</h3><div class="comment"><strong>阿秀</strong>: 已私聊回复，下次提问可以点左上角的去提问按钮，这样系统会给我发通知。</div></body>
</html>
