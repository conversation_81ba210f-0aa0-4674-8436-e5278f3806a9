
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0054_2025-02-19_21-45</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0054_2025-02-19_21-45</h1>
<div class="meta">xin - 2025-02-19 21:45:25</div>
<div class="content">秀哥您好<br/>我是24届毕业生，目前就职于小厂做数据库内核开发，工作内容较杂，近期甚至和运维差不多，项目经验相对缺乏，个人成长空间有限。最近准备跳槽找大厂的C++后台 或者 数据库的岗位，有以下问题想请教一下：<br/>1. 在缺乏项目经验的情况下怎么准备项目？ <br/>2. 以下是我校招时使用的项目，项目内容也比较杂，请问该怎么优化一下用到社招中？</div>
<img src="../图片/0054_2025-02-19_21-45_0.jpg"/><div class="comment"><strong>【阿秀】回答：</strong><br/>学弟，你做的是数据库内核开发的工作，这个类别就决定了你很难在短时间内出成果，你需要一直耕下去才会有收获，你最近半年没什么可以做的和没什么可以写在简历上的内容也就不奇怪了。。。<br/><br/>1、你可以去网上找付费的项目，但这个容易被割韭菜，如果真的要这么做的话仔细甄别哦。<br/><br/>另外知识星球置顶帖资源沉淀中有一个c++脚手架项目，适合校招和毕业2年之内的同学使用，去年已经有24届的同学社招用上了，可以看看，文档和代码都是齐全的。<br/><br/>2、MIt6824、CMU 15-445这几个本来就在db领域很知名，这个没的包装，我建议你把时间改一下，改到你工作后的时间，就说是你工作之余自己看书充电补充做的项目。<br/><br/>百度飞浆这个也把时间写近一些，可以写在毕业最后一年，就说是在校期间做的项目。<br/><br/>蚂蚁金服这个老老实实说是实习期间做的项目好了。<br/><br/>另外，建议你重新做下简历，把简历内容写完善一些，写到一页半的样子，不必遵守什么一页简历原则。<br/><br/>要把你的项目和过往写充实一些，不要写的这么省略，你可以看下星球置顶帖知识图谱中的从0教你写简历系列，尤其是其中的实习和项目怎么写。</div></body>
</html>
