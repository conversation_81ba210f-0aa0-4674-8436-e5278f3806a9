
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0020_2025-01-07_21-07</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0020_2025-01-07_21-07</h1>
<div class="meta">声望 - 2025-01-07 21:07:28</div>
<div class="content">秀哥你好，我是25届毕业生，现在找工作遇到了比较大的问题，比较迷茫，希望秀哥指点。<br/>我是联合培养的研究生，23年7月开始联培，因此没有正经的实习经历，主要是做研究，但也帮忙做了一些项目，做的项目都和java无关，我在的单位是个研究所，不专门开发软件，小作坊，自己做的项目是参照学成在线写的，挂了研究所的名。然后在7月份开始准备秋招，投了一些公司，大中小厂都有，进了面的大部分都是一面挂，这个状况一直持续到9月底。之后我自己觉得，因为做的是研究所给的课题，再闭门造车可能写不完大论文，就先专心把研究内容做完了。<br/>现在继续准备找工作，我的初步想法是先找一份和代码开发相关的工作，想请秀哥看下简历，我有几个问题想请教：<br/>1、我还能接着找java开发吗？要不要换方向？如果完全不考虑待遇、地点、个人发展等因素，怎么做可以最快找到工作？<br/>2、现有的项目有什么问题？需要做新项目或者更换项目吗？<br/>3、因为项目是自己编的，并没有实际部署和使用的经历，如果被面试官问到实际生产部分的内容怎么办？之前有很多次面试被面试官问到露馅，认为我的项目不够实际。</div>
<img src="../图片/0020_2025-01-07_21-07_0.jpg"/><div class="comment"><strong>【阿秀】回答：</strong><br/>师弟你好<br/><br/>所以我理解你是研究内容这些基本已经搞定了是吗？大小论文都基本搞定了？其实只要有小论文后面就好办，大论文就是时间的问题。<br/><br/>先说你的简历问题，校招不要把专业技能放在那么靠后的位置上，可以考虑放在教育背景下面。<br/><br/>1、可以继续找Java，你的背景和技术都能够支撑你找到Java相关的工作。个人不建议换其余方向，Java后端竞争激烈进不去，可以去投测开，其余方向就不要考虑了。<br/><br/>“如果完全不考虑待遇、地点、个人发展等因素，怎么做可以最快找到工作？”：去投测试，是个公司你就去投测试，被问就说感觉自己不喜欢开发，所以来做测试了。<br/><br/>2、不用，你这个项目就可以，你应该做的是自己试着部署一下这个项目，而不是想着换其余项目。你这个项目中有spring、rbac、任务调度、redis这些，不算很拉，至少没你想得那么拉。如果真要换，也可以，换个有微服务的，也可以。<br/><br/>3、你大多数都是一面挂，一般来说一面一般不会问很深的项目内容，一面主要是考察计算机基础，比如os、net、db和编程语言这些，尤其是大厂，更是如此，看了你的描述我觉得你可以试着多在计算机基础和八股文这些上使使劲。<br/><br/>如果被问到实际生产环境，你可以说这个项目是师兄以前买的付费项目或者自己跟着B站做的项目，我是跟着视频做过来的，没经过实际生产环境验证，不要给自己挖坑，没经历过就老实说没经历过，说实习期间做的不是相关的项目。</div><hr/><h3>💬 评论 (3条)</h3><div class="comment"><strong>声望</strong>: 感谢秀哥的回答，我的大小论文都搞定了。<br/>这个项目之前尝试部署过，但是学成在线项目的代码和依赖比较久远，功能能运行但是没法打包部署，需要换代码实现吗（用比较新的类似项目替代）？<br/>还有项目是否真实存在的问题，请问秀哥到底应该说真话（不是真的项目，做了练手的）还是假话（在研究所内部部署运行）？</div><div class="comment"><strong>阿秀</strong> 回复 <strong>声望</strong>: 即使这个项目无法部署，你也应该找一个相似技术栈的成熟项目去试着部署一下，可以去github上看看，一些文档比较齐全的项目都会写如何部署；如果你能部署成功，那可以考虑说后者，说研究所内部运行，用户数也不多，就内部人员在用，如果部署不成功，那还是老实说是练手的吧，别给自己挖坑了。</div><div class="comment"><strong>声望</strong> 回复 <strong>阿秀</strong>: 好的，谢谢秀哥</div></body>
</html>
