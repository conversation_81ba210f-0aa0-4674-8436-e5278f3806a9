
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0072_2025-02-26_14-58</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0072_2025-02-26_14-58</h1>
<div class="meta">柒 - 2025-02-26 14:58:13</div>
<div class="content">秀哥你好，我目前双非研一在读，本科期间c++学的比较多，操作系统和计算机网络也准备了不少，最近也刚写了个reactor服务器，但是实验室是搞嵌入式的，现在被导师安排在一家小公司实习，也是搞嵌入式的。现在不知道以后就业该准备c++还是嵌入式，嵌入式的话之前简单搞过一个linux板子</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>师弟你好<br/><br/>“被导师安排在一家小公司实习”：是你导师接的横向项目吗？这种研一就要做项目的，一般可能整个研究生三年又两年半都要做项目。。。。<br/><br/>既然你实验室是做嵌入式的，现在做的横向也是嵌入式相关，以后的求职方向划算走嵌入式的，况且嵌入式这几年薪资水涨船高，已经不比互联网少了，建议走嵌入式会好一些。</div><hr/><h3>💬 评论 (1条)</h3><div class="comment"><strong>柒</strong>: 谢谢秀哥的建议</div></body>
</html>
