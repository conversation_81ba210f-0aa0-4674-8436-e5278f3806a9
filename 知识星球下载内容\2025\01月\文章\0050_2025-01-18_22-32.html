
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>0050_2025-01-18_22-32</title>
            <style>
                body { font-family: 'Microsoft YaHei', <PERSON>l, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
        </head>
        <body>
            <h1>0050_2025-01-18_22-32</h1>
            <div class="meta">肥猫飞天 - 2025-01-18 22:32:21</div>
            <div class="content"><span style="color: #ff6b6b; font-weight: bold;">##每日一点点##</span> <br>day1<br>计算机是怎样跑起来的 1-3章<br>大话数据结构 第2章<br>网络是怎样连接的 第1章<br>JAVA 继承 多态 单例设计模式 枚举 抽象类 接口<br>黑马视频P60-65 P66-69 P70-82</div>
        </body>
        </html>
        