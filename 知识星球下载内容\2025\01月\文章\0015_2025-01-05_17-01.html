
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0015_2025-01-05_17-01</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0015_2025-01-05_17-01</h1>
<div class="meta">Que Sera - 2025-01-05 17:01:30</div>
<div class="content">人生中第一次实习，音视频开发实习第一天，很焦虑，啥也不会，每周好像还要开周会汇报进度。mentor让我先配环境，是公司做好的一个Android端项目，各种报错，见都没见过，而且根本没学过的东西，flutter啥的。。。。  周六加了一晚上班也没鼓捣出来，各种看不懂，唉。。。。😔<br/>怕组长催我产出😫😫😫😫焦绿</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>[捂脸]所以要去实习，因为实习有机会去见识到以前你从没见过的项目和问题，有阻碍才有进步的可能。<br/><br/>你现在遇到的问题过一个月后你再来看就会发现其实很简单，不要慌，实习的第一要义就是遇到困难即使往上汇报，要给组长留出缓冲时间buffer，遇到问题后就试着自己解决，如果解决不了，就看看是卡在哪了，找对应的人去寻求一下帮助、<br/><br/>对于新人菜鸟，大家基本都能理解的，不要焦虑。。。必要时候，你的组长甚至会出手替你做需求。</div></body>
</html>
