
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0019_2025-02-07_17-54</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0019_2025-02-07_17-54</h1>
<div class="meta">Mt - 2025-02-07 17:54:12</div>
<div class="content">秀哥，你好，我先说一下我的基本情况：<br/>双非科班本硕，秋招找的C++没上岸，后面12月份找了个小的游戏公司实习，做的是golang服务端开发，实习到现在，做的基本上都是业务开发。<br/>当时选择这个实习有几个原因，最主要的原因是我个人没有实习经历，决定攒一段实习经；其次是golang的实习，我正好可以学习golang，春招也可以c++和golang一起找；最后是我个人也比较喜欢游戏。<br/>然后实习到现在，我个人觉得不喜欢的有三点：<br/>一、实习了两个月是学到不少东西，但是感觉当前公司用的框架有点不适用其他领域的服务端开发（对日后跳槽没有优势）。<br/>二、没有办法平衡生活，春节前，游戏准备上线的那一段时间，连续三周都在加班，最早都是9 10 6。<br/>三、是感觉服务端在游戏行业不算是很重要的岗位，因为目前项目组只有3人是服务端的，客户端人数10+，日常的时候服务端很快就能干完，服务端要等客户端干完才能继续推进。<br/>我个人本来是打算二月中旬就跑路的，因为目前的任务是准备春招还有毕业大论文，而且以后肯定是回广东工作，不打算留在这里。但是今天的时候，领导说准备把我调去另外一个新游戏项目，从零开始跟着做，然后我又觉得可能能学到东西。<br/>我现在的问题是我不知道该不该提前跑路，还是跟着新项目再做一会再跑路。目前我对自己以后的职业规划应该是不会再考虑游戏行业了，应该想找一些尽可能就业面广的职位。希望秀哥给点建议。</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>师弟，你的目的是攒一段实习经历，不是做其余的，所以第1、2个问题都可以往后放一放。<br/><br/>尤其是第一个问题，每个公司都有自己的技术栈和框架，很少有能切到下家时无缝衔接的，所以框架不同不是什么大问题，技术在身就行，东西都是那些东西，这个不要担心。<br/><br/>游戏行业就是这样的，临上线前很辛苦，上线了就好一些了。包括一些搞电商的也是，每年618、双十一、双十二前都很辛苦，3-4轮压测。。。<br/><br/>我看你是12月份实习的，到现在二月初，中间还穿插着过年放假，满打满算应该最多就实习两个月的时间，我建议你试着把这段经历写出来，总结一下自己的工作，然后把这段经历写在简历上，如果能写出来，面试的时候你也有东西说，那可以提离职，早点止损；<br/><br/>如果不行，可以再试着再跟两周，也就是到二月下旬这个样子，到二月下旬可以考虑走人了。你还有大论文和春招，可以考虑回去准备这些东西了，要不后面你的时间会比较紧张。</div><hr/><h3>💬 评论 (1条)</h3><div class="comment"><strong>哆啦A梦</strong>: 想问一下c＋＋怎么找go呀？</div></body>
</html>
