
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0056_2025-02-20_20-34</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0056_2025-02-20_20-34</h1>
<div class="meta">小鱼儿 - 2025-02-20 20:34:35</div>
<div class="content">秀哥，想请问一下，我学的是C++，想投递接下来的暑期实习。但是感觉C++太卷了，我可以投递ios客户端岗位吗，但是我没有学过ios客户端的相关知识，只是了解到他和C挺像。想请秀哥给个建议（秀哥，我的简历发你邮箱了，有时间可以帮忙看看吗，谢谢秀哥了）</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>可以，很多公司都会招C++开发去做ios开发，因为ios开发的语言跟cpp比较像，所以很多ios开发都是cpp转过去的。<br/><br/>简历应该已经改完了，修改意见在回复邮件里，可以看下你的邮箱。</div><hr/><h3>💬 评论 (1条)</h3><div class="comment"><strong>小鱼儿</strong>: 感谢秀哥！刚刚去看邮件，建议写得好详细，好感动，谢谢秀哥！</div></body>
</html>
