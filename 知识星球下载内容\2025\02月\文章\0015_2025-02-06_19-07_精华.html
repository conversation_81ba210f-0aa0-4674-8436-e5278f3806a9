
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0015_2025-02-06_19-07_精华</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0015_2025-02-06_19-07_精华</h1>
<div class="meta">Troub1eMak* - 2025-02-06 19:07:43</div>
<div class="content">秀哥下午好，我主要是来询问一下关于知识星球那个脚手架项目的问题，今天早上经历了春招第一次面试，面试官问我这个脚手架的应用场景，我说的大概意思是需要高性能通讯的场景。面试官说现在已经有很多成熟的服务器技术可以完成这个了，完全没必要再用你这个。<br/>第二个问题是大概意思是高性能体现在什么方面，我就回答的那些技术亮点，面试官问我具体效率提升了多少。有没有实际应用场景，你是怎么测试的。这俩问题我回答的特别差，我也不知道该怎么回答，我秋招时的面试不多，他们一般就是问的技术细节和实现细节，但这个面试官一直追着问我应用场景和意义。求秀哥指点一下类似问题的回答思路。谢谢秀哥!</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>第一个问题你就不应该这么回答，就好像大学里一些比赛老师评委说你的xxx有什么意义吗？现在都已经xxx了，何必再用你的这个？<br/><br/>你应该说”确实存在很多新的服务器技术可以完成这个内容，但不管什么技术都不是一蹴而就的，没有空中楼阁这回事，再厉害的框架或者项目都是先有一个大体，然后再这个大体上删删改改，逐步迭代起来，直到后来很好用。<br/><br/>我做的项目是简易版的，就是刚起步的那个阶段，我不做这类起步阶段的项目，难道一蹴而就可以直接做成一个功能很完善、文档很齐全的框架吗？也许我以后会有这样的能力，但现在我认为自己还不具备这样的技术，还需要继续进一步沉淀和学习，谢谢面试官的指导。“<br/><br/>个人认为学会两位数的加减法的前提应该是先学会一位数的加减法，而不是跳过这一步。<br/><br/><br/>第二个问题，你应该提前测量一下性能，可以试试</div></body>
</html>
