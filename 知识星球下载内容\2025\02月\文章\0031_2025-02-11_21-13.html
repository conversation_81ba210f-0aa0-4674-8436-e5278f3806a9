
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0031_2025-02-11_21-13</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0031_2025-02-11_21-13</h1>
<div class="meta">长亏 - 2025-02-11 21:13:54</div>
<div class="content">秀哥你好，我是非科班的，末985硕，学习的是c++方向。目前学习进度操作系统、计算机网络、数据结构都已学习完毕，现在主要就是结合你的校招笔记还有小林coding复习，然后不会的再去回顾;leetcode之前刷了代码随想录两遍，现在正在重新刷题，c++语言方面学习完了c++ prime plus,c++ prime并没有系统学习，只是遇到不明白的地方在进行详细学习。<br/>我目前是已经延期毕业半年了，现在小论文已经接受了，大论文已经写完了，可以赶上4月份或6月份的学位申请。看了星球里的项目推荐，准备这两个项目，分别是高性能通讯手架项目和CS144大作业。<br/>我并没有参加过实习，我现在应该把学习的重心放在哪里?是要先投实习还是直接投简历？<br/>另外，除了Mysql、Redis、STL源码剖析和设计模式之外，还需要学习别的东西吗？</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>师弟，你现在应该重点抓刷题 + 八股文这块，然后把简历写好，三月份试着去找实习，这是你接下来一段时间应该重点做的事。<br/><br/>Mysql、Redis、STL源码剖析这几个可以看，设计模式不用急着学，看一下工厂以及单例模式就基本够用，考察设计模式也不会脱离这两个。</div></body>
</html>
