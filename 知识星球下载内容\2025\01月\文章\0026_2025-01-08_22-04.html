
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0026_2025-01-08_22-04</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0026_2025-01-08_22-04</h1>
<div class="meta">神秘灬メ范儿 - 2025-01-08 22:04:35</div>
<div class="content">秀哥您好，25届毕业生，部门入职的时候可以选Java或者c++，但自己目前只会c++，还有四五个月毕业，是学下Java方便后续跳槽呢？还是深入学习c++呢</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>”部门入职的时候可以选Java或者c++“：这个应该是要跟你做的业务有关，你选什么业务然后才是用什么语言，而不是你用什么语言区决定你做什么业务。<br/><br/><br/>如果有的选的话，建议可以去实习一两个月，提前去占个业务坑，而不是毕业后再去入职，那样选择权可能就不多了。<br/><br/>如果无法去实习的话，你应该去问有哪些业务可以选，选一个好的业务，然后再问这个业务的技术栈是什么，才决定你是继续写cpp还是去学java的、</div></body>
</html>
