
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0008_2025-02-05_22-17</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0008_2025-02-05_22-17</h1>
<div class="meta">量子绝缘体 - 2025-02-05 22:17:45</div>
<div class="content">秀哥您好，想问问您对华为OD了解吗？<br/><br/>个人情况：本人24软工双非毕业，社畜一枚，现在在西部计划内，从24毕业到现在干了半年多了吧。当初是在家人的推荐下选择走体制内这条路，但实际干下来我觉得这不适合我，微薄的工资，领导的无能推责，同事的不作为，感觉我在单位就是个抗伤害的，反正各种错都往我身上推，每个月是稳定打款啊，可是志愿者微薄的补贴根本不够。总之各方面都有原因吧。干的非常难受。<br/><br/>今年8月份我的合同就到期了，我应该是可以提前走的，所以在考虑我以后想要走的路。<br/>在单位唯一学到的东西就是让我意识到学历很重要，有些人能力和本科差不多甚至有些方面不如本科，但就因为是研究生就可以无条件被分到好单位<br/>。<br/>家里没有太多文化，条件不好，强调毕业了就工作，我大学时的绩点还算不错，当时导员跟我说保研的事情我都没有在意，一直受家里面的影响想的是毕业就工作，根本没考虑考研的事情。可现实总是事与愿违，这半年来的经历给予我沉重的打击。让我坚定了一定要提升学历的信心，哪怕是最后是双非，自己努力，读研三年的技术沉淀，我认为一定能有个不错的结果。<br/><br/>但家里收入不太乐观，我每个月的钱除了生活费还要补贴家用，脱产考研根本不现实，但我认为只有脱产考研上岸的几率才大。于是想先存钱靠自己再脱产。<br/><br/>逛招聘软件时发现了华为OD这个职业，应该是外包吧，听说挺辛苦但能挣到钱，我也不要求晋升，只要能挣钱我就能干，最后的目标是存够了钱就脱产考研。<br/><br/>然后现在的打算是多花点时间刷机试算法题，秀哥您觉得我这个想法怎么样。请秀哥指教。</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>考研这几年性价比其实很低，并且肉眼可见的接下来几年性价比会持续走低下去。<br/><br/>如果你有觉悟从踏入校门的那一刻就过三年苦行僧的日子，研一研二有机会去实习就一定尽力争取去，而且读研三年要花很多时间和精力去学计划，你如果有这个心理准备和觉悟的话，那可以按照你的计划去走，不管什么时候有一颗想往上走的心肯定不是坏事。</div><hr/><h3>💬 评论 (8条)</h3><div class="comment"><strong>归寻</strong>: 唉，哥们辛苦了。</div><div class="comment"><strong>量子绝缘体</strong>: 好的秀哥，感谢您宝贵的建议。目前正在根据您的java路线复习java后端知识，不管怎么说，还是先把底子打好，不然进去了也过不了试用期。趁着体制内还有点时间给生活兜底，抓紧学习吧。共勉[抱拳]</div><div class="comment"><strong>量子绝缘体</strong> 回复 <strong>归寻</strong>: 家家有本难念的经，共勉，大家一起努力，迎接一个更好的未来。</div><div class="comment"><strong>只是橘色仍温柔</strong>: 佩服哥的好心态，我摊上个导师就够心态爆炸了，我很难想象我遇到你的情况会怎么样</div><div class="comment"><strong>量子绝缘体</strong> 回复 <strong>只是橘色仍温柔</strong>: 生活总会磨平当初的棱角，现在才明白有时间什么都不管，沉浸学习是多么珍惜，才是最容易出人头地的路子，很多人却弃之如敝履。</div><div class="comment"><strong>空白</strong>: <span style="color: #007acc; font-weight: bold;">@@%E9%87%8F%E5%AD%90%E7%BB%9D%E7%BC%98%E4%BD%93</span> 大佬好，存够钱再考研的话会有年龄焦虑吗？我目前感觉自己存下一点钱了，但是计算机年龄大了比较焦虑，希望大佬能再分享一下看法。🙏</div><div class="comment"><strong>量子绝缘体</strong> 回复 <strong>空白</strong>: 其实我自己也不知道是否值得，我只知道提升学历对我来说就是很重要的，我02年尾巴，目前22岁吃23岁饭，就说打2年工25岁，研究生出来28，29，离计算机年限35还是有些年份，最重要的是我认为年龄的差距是有，但提升自己技术才是最根本的核心竞争力，考研也是在学习，一直不断往上学习，不管什么时候我都觉得计算机是个终身学习的行业，既然都是学习那为什么不顺便多学点搞个研究生提升一下呢？年龄已是既定的事实我们无法改变，我们只能够在有限的年龄中尽可能的去抓紧时间学习，提升自己的核心竞争力，才能在未来有的一口饭吃。<br/>仅个人看法[抱拳]</div><div class="comment"><strong>空白</strong> 回复 <strong>量子绝缘体</strong>: 谢谢大佬解答[抱拳]</div></body>
</html>
