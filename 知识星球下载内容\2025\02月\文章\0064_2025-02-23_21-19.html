
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0064_2025-02-23_21-19</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0064_2025-02-23_21-19</h1>
<div class="meta">O_O - 2025-02-23 21:19:04</div>
<div class="content">秀哥您好，我是25届毕业生，刚考完研，目前觉得成绩不太理想，想尽快的找实习，我学历也不怎么样，专升本到天津城建大学的，我目前的打算就是在春招找一个c++或者qt开发的实习。<br/>我现在把c++黑马的那个课程学完了，qt剩5天就要学完了，上个月还把unix环境高级编程对着敲了一半知道自己还差很多，但是我没有实习经历，我不知道继续该怎么学了。准备找一个项目做一下但是发现自己无从下手，有种摸不着头脑的感觉，请问秀哥有没有什么建议。</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>学弟，建议优先考虑QT开发，qt属于薪资不高，但绝对有坑位的那种岗位。cpp后端开发比较难，竞争压力可能会比你想的要大一些。<br/><br/>做项目最好的就是找一个视频跟着敲，尤其是刚开始的时候，不要去看书或者直接去找源码去生啃源码，建议去B站或者网易云课程之类的去找个QT项目带学的视频，比如中国象棋或者网盘之类的都可以，千万不要自己去啃书做项目。</div><hr/><h3>💬 评论 (1条)</h3><div class="comment"><strong>O_O</strong>: 谢谢秀哥指导</div></body>
</html>
