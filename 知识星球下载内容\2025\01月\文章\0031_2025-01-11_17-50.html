
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0031_2025-01-11_17-50</title>
<style>
                body { font-family: 'Microsoft YaHei', <PERSON>l, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0031_2025-01-11_17-50</h1>
<div class="meta">切糕子 - 2025-01-11 17:50:43</div>
<div class="content">段错误（吐核）是什么error啊？[苦涩]</div>
<img src="../图片/0031_2025-01-11_17-50_0.jpg"/><hr/><h3>💬 评论 (2条)</h3><div class="comment"><strong>奔行世间</strong>: 数组越界或者指针访问了不该访问的地方</div><div class="comment"><strong>衍</strong>: 一般是越界了或者空指针之类吧，可以用gdb调</div></body>
</html>
