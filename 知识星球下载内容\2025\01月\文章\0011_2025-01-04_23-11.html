
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0011_2025-01-04_23-11</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0011_2025-01-04_23-11</h1>
<div class="meta">luff - 2025-01-04 23:11:08</div>
<div class="content">秀哥你好，首先介绍一下我的背景。我是24届某双非本科毕业生，刚考研二战结束，对完答案感觉有希望进复试，但希望不是很大。目前只会一些408的一些东西，工作相关的技术几乎没怎么学。<br/>我现在想复试和工作一起准备。所以有两个想法，<br/>1、因为目标院校的复试只需要笔试C++就行，没有上机考试，所以我想一边学C++基础，一边学习C++相关的就业知识，然后做个C++相关的小项目，不知道是否可行。若是有幸进入复试也可以把该项目写入简历里面。但我也了解到C++入行有难度。。。<br/>2、只学C++基础来准备复试，然后另学其他相对容易入行的技术来准备找工作。因为我要是二战还是失败我有可能三战，所以想先找份工作赚钱。<br/>最后，不管我上述的想法，秀哥有什么其他的建议吗？</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>学弟，我是建议尽量复试，如果初试学校进不去，那就积极去调剂，去小木虫、硕博之家这类论坛多看那些硕博招生的帖子，像货物一样把自己推销出去。<br/><br/>如果能找到个学上就尽量去上，你最大的问题是没有时间去缓冲学习了，虽然这几年读研的性价比变低了不少，但如果能上岸，就可以有2-3年的时间去系统学习了。<br/><br/>1、可以，你也可以去看看星球置顶帖「知识图谱」中的cpp项目推荐文章以及置顶帖「资源沉淀」中的cpp速成项目，可以去看看。<br/><br/>2、老实说，不太建议去三战了，沉默成本过大，而且现在读研性价比比较低，再过几年的行情可能会更差一些，早入行比晚入行好不少。<br/><br/>我的建议就是二战尽量上，找个调剂学校也去上，复试的时候能找个放手的老师更好，读研期间争取去实习，而不是把心思放在三战上。</div><hr/><h3>💬 评论 (1条)</h3><div class="comment"><strong>luff</strong>: 好的</div></body>
</html>
