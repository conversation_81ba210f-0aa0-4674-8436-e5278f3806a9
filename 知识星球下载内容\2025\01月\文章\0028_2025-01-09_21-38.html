
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0028_2025-01-09_21-38</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0028_2025-01-09_21-38</h1>
<div class="meta">never end - 2025-01-09 21:38:07</div>
<div class="content">秀哥您好，之前有在知识星球向您请教学习路线的问题，我现在是研二，目前的学习进度：看完了黑马的c++课程、《<a href="https://wx.zsxq.com/mweb/views/weread/search.html?keyword=c++primer" style="color: #007acc;">c++primer</a>》大致看完了一遍、然后侯捷老师的几套课程大概看了一遍，但是感觉忘得基本差不多了，然后计算机学科方面看完了王卓老师的数据结构，操作系统在看南京大学袁春风老师的课程，同时跟着代码随想录在刷题，目前大概刷到了二叉树。<br/>	1.想问一下秀哥这个进度怎么样，会不会太慢了，现在感觉自己还有好多没学，比较焦虑。<br/>	2.然后想问一下秀哥，计算机这几门学科是需要每一门找一个网课看，然后认真学，还是到时候快要面试的时候，直接背八股，然后不懂的地方再现学呢，因为听周边同学讲，每一门都认真看网课学习太费时间了。<br/>	3.还有一个就是听学长学姐说，如果想实习的话，寒假就可以开始准备了。我是要继续学习计算机基础学科（计算机网络这些），还是先打好基础还是直接寒假做一个项目，下学期投实习试一试呢？<br/>	4.还有就是这个时间段了，我是直接准备暑期实习，还是在找暑期实习之前投日常实习试试看呢，但是感觉自己还有好多没有学，如果去实习的话，就没有什么时间学习了。</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>师弟你好<br/><br/>1、这个进度不算慢，但也不算快，我在你这个时候差不多也是你这些内容。但如果你打算去找实习的话，是有点慢了。<br/><br/>2、我的建议是前者，你现在不学以后基本也没机会去学了，甚至可以说这是你最后一段可以好好沉淀打基础的时光了。<br/><br/>不要一直看视频，看着看着就乏了，我当初的做法是看几P就看看书上对应的内容，这样做的好处是可以帮你理解，坏处自然是比较费时间了。你直接背八股不是不可以，但面试中如果遇到厉害的面试官，被追着问，可能会露馅。。。<br/><br/>3、是的，直接做项目对于想要找日常实习的你来说性价比更高一些。<br/><br/>4、能去日常实习就先去日常实习，实习的重要性和优先级是高于自己埋头苦学的，尤其你导师放你去实习的情况，好好利用。<br/><br/>星球置顶帖知识图谱中有cpp项目推荐文章，置顶帖资源沉淀里也有速成项目，可以看看。</div><hr/><h3>💬 评论 (3条)</h3><div class="comment"><strong>never end</strong>: 那对于现阶段的我来说，秀哥有比较推荐的项目吗，因为您帖子里面提到的几个项目涉及到的东西我都不太了解，如果时间有限的情况下，想问一下秀哥比较推荐哪个项目呀</div><div class="comment"><strong>Zeroxus</strong> 回复 <strong>never end</strong>: 搞懂一个脚手架其实就够找实习了，另外再写个实验室项目，我就是这样的。</div><div class="comment"><strong>never end</strong> 回复 <strong>Zeroxus</strong>: get了，谢谢！</div></body>
</html>
