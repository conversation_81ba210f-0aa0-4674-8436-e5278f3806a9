
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0010_2025-02-05_22-49</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0010_2025-02-05_22-49</h1>
<div class="meta">橙汁 - 2025-02-05 22:49:03</div>
<div class="content">秀哥，之前十月是签了一家offer，现在是提前实习第四周了，说实话这家考核强度还是比较大，三个月考核计划基本没有空闲的时间，而且刚开始就接触最难的产品线写组件了，mt从刚进来就开始说淘汰率很高，压力也比较大，虽然是客户端开发但是产品代码量太大了，组件化也很复杂，公司挺好的，说实话还想努力努力转正，但是又怕后面考核没过再准备春招会不会太晚了</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>考核转正？你签的不是秋招offer吗？还是说那种必须来实习，然后转正通过才算正式offer的吗？<br/><br/>如果是前者，你现在来提前实习是来熟悉组内技术栈的，应该不存在什么考核这种事。<br/><br/>你换一步想，如果你没去实习的话，你毕业后直接去工作了，所面临的情况跟你现在类似，到时候你上手会更慢，压力也会更大；你现在提前来实习了，熟悉组内业务和技术栈了，那么等你毕业后再来工作，上手难度就会小很多了，，。。而且提前实习也能有机会占一个更好的业务坑。<br/><br/>所以我建议签完秋招offer的应届生尽量提前实习，有很多隐形好处的。</div><hr/><h3>💬 评论 (1条)</h3><div class="comment"><strong>橙汁</strong>: 是秋招offer，但是要求提前实习，然后实习合同上如果考核不达标就需要解约这种</div></body>
</html>
