
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0020_2025-02-07_18-56</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0020_2025-02-07_18-56</h1>
<div class="meta">Six feet u* - 2025-02-07 18:56:59</div>
<div class="content">秀哥你好，秀哥你回答后，我结合自身情况想了下，想问下我采用哪种策略会比较合适，<br/><br/>现状是，我领导跟我讲，公司的资金不足，按目前的规模只能支撑4个月不到了，后续情况还不好会裁员。<br/><br/>我自己是有意愿不继续游戏行业，想转服务端开发，但我目前积累的工作技术只有cpp，互联网要求的中间件redis什么的，公司完全没用，所以想问问秀哥，<br/><br/>如果我现在开始直接找工作，感觉大概率还是老路，会回到游戏服务端。<br/>下家不想游戏行业了，是不是只能选择从现在开始学Java这一块，然后哪怕被裁员了，也先gap着学一段时间后再找。<br/><br/>这里还想问下秀哥，转互联网服务端开发是不是按照星球Java那条路线准备就行了<br/><br/>还是秀哥有其他看法能指点下😂，目前我自己只能想到这些</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>对，按照这条路线走下去基本够你找工作了。<br/><br/>还有，25年不要乱动，能拿一个月工资先拿一个月工资再说，今年经济会很差。。。</div><hr/><h3>💬 评论 (3条)</h3><div class="comment"><strong>贰柒伍</strong>: 秀哥，想问下为啥25年很差呢？25年还适合跳槽吗[破涕为笑]</div><div class="comment"><strong>阿秀</strong> 回复 <strong>贰柒伍</strong>: 外贸这条路因为关税已经被堵死了，只剩下内销了，国内经济去年啥样用说吗。。。另外，这几天黄金暴涨你应该也刷到过类似的新闻了，乱世黄金，越乱黄金才越值钱，所以反推一下你也能知道经济啥样、外面啥样了、、</div><div class="comment"><strong>贰柒伍</strong> 回复 <strong>阿秀</strong>: [撇嘴][撇嘴]get了</div></body>
</html>
