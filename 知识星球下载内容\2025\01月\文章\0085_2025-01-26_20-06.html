
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0085_2025-01-26_20-06</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0085_2025-01-26_20-06</h1>
<div class="meta">HUMBLE. - 2025-01-26 20:06:04</div>
<div class="content">秀哥好，我想问一下嵌入式软开的话cpp要怎么学呢？是要跟cpp后端那样的学各种算法还是说只把基础学好后直接上板子码代码 跑代码呢？谢谢秀哥</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>可以去我公众号回复 “嵌入式”，有一些前面学长学姐留下来的嵌入式学习经验，可以看下他们的学习经验分享。<br/><br/>嵌入式对于算法的要求是没有后端高的，这个可以放心。</div><hr/><h3>💬 评论 (4条)</h3><div class="comment"><strong>HUMBLE.</strong>: 好嘞好嘞 谢谢秀哥</div><div class="comment"><strong>HUMBLE.</strong>: 秀哥我还想问一下嵌入式开发 刷力扣要该怎么刷</div><div class="comment"><strong>阿秀</strong> 回复 <strong>HUMBLE.</strong>: 跟开发岗没什么区别，考察的都是那些内容，按照开发岗的去刷就行。</div><div class="comment"><strong>HUMBLE.</strong> 回复 <strong>阿秀</strong>: okk</div></body>
</html>
