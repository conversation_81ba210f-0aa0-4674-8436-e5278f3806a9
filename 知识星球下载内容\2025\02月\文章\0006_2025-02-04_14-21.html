
<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>0006_2025-02-04_14-21</title>
<style>
                body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
                .meta { color: #666; font-size: 14px; margin-bottom: 20px; }
                .content { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin: 10px 0; }
                .comment { background-color: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #007acc; }
                .files { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
            </style>
</head>
<body>
<h1>0006_2025-02-04_14-21</h1>
<div class="meta">404 - 2025-02-04 14:21:44</div>
<div class="content">阿秀前辈你好，最近感到很焦虑，有一些问题向你请教<br/>个人基本情况：大三生，末流211，编程语言（在学校里学的C但是学的很烂，想转向学go）；数据结构与算法（能看懂，但是自己写不出来，最近开始刷算法题但是基础太差很吃力）；计算机网络（理解常见的概念）；数据库（能实现基本的增删改查但是用的是SQL sever）；操作系统（了解基本的概念类似于死锁、进程、线程，理解银行家算法，信号量）；计算机组成原理（学过的基本上都忘记了）；英语较差（只过了四级）；没有项目经验；没有比赛经历；个人语言交流能力一般，容易紧张。<br/>问题：1.前辈，我是否应该放弃暑期实习，直接全力准备秋招？<br/>2.前辈，我接下来应该怎么学习呢？</div>
<div class="comment"><strong>【阿秀】回答：</strong><br/>1、学弟，我是建议不要放弃暑期实习的，现在是二月初，暑期实习五六月也有在招的公司的，满打满算也还有四个月的时间，直接放弃暑期实习是不明智的。<br/><br/>从去年的情况来看，实习经历的在秋招中会加分不少，当然并不是说没有实习经历秋招就找不到工作，拿不到offer了，但有绝对会比没有的好。<br/><br/>2、如果你打算学Golang的话，那更应该去实习了，Golang在秋招中的hc是不如cpp和java的。你的基础相当于就是正常上课，没做什么其余的学习，现在开始认真学还是来得及的，跟着星球置顶帖知识图谱中的golang学习路线以及校招基础学科学习路线走就行。<br/><br/>多说一句，计算机组成原理性价比比较低，可以放在最后学，甚至不学也没什么问题，基本不会被问到。</div><hr/><h3>💬 评论 (2条)</h3><div class="comment"><strong>404</strong>: 好的，谢谢阿秀前辈</div><div class="comment"><strong>阿秀</strong> 回复 <strong>404</strong>: 叫我阿秀就行或者秀哥，都可以的。不敢妄称什么前辈，不过是早生了几年，多吃了几年干饭，有些事情，你过几年工作了也会懂得的。</div></body>
</html>
